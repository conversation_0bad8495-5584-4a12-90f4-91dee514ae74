#!/usr/bin/env python3
"""
Test the FIXED codebase selection logic
"""

import requests
import json
import time

def call_api(query, description):
    """Make an API call and analyze response"""
    
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    model = "llama3:latest"
    tool_id = "code_analyzer_tool"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": model,
        "messages": [{"role": "user", "content": query}],
        "tool_ids": [tool_id],
        "stream": False
    }
    
    print(f"\n🔧 {description}")
    print(f"   Query: '{query}'")
    
    try:
        start_time = time.time()
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=120
        )
        end_time = time.time()
        
        print(f"   Status: {response.status_code}, Time: {end_time - start_time:.2f}s")
        
        if response.status_code == 200:
            result = response.json()
            choices = result.get("choices", [])
            
            if choices:
                content = choices[0].get("message", {}).get("content", "")
                
                print(f"   Response Length: {len(content)} chars")
                print(f"   First 200 chars: {content[:200]}...")
                
                # Analyze response quality
                has_real_code = "tmwmem" in content.lower()
                has_error_msg = "no codebase selected" in content.lower()
                has_context = "context" in content.lower()
                is_generic = any(word in content.lower() for word in [
                    "tensorflow", "general programming", "typical implementation"
                ])
                
                print(f"   Real Code (tmwmem): {has_real_code}")
                print(f"   Error Message: {has_error_msg}")
                print(f"   Has Context: {has_context}")
                print(f"   Generic Response: {is_generic}")
                
                # Overall assessment
                if has_error_msg:
                    quality = "NO_CODEBASE"
                elif has_real_code and not is_generic:
                    quality = "EXCELLENT"
                elif has_context:
                    quality = "GOOD"
                elif is_generic:
                    quality = "GENERIC"
                else:
                    quality = "UNCLEAR"
                
                print(f"   Quality: {quality}")
                
                return {
                    "success": True,
                    "content": content,
                    "quality": quality,
                    "has_real_code": has_real_code,
                    "has_error_msg": has_error_msg,
                    "response_time": end_time - start_time
                }
            else:
                print("   ❌ No choices in response")
                return {"success": False, "quality": "FAILED"}
        else:
            print(f"   ❌ HTTP Error: {response.status_code}")
            return {"success": False, "quality": "FAILED"}
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
        return {"success": False, "quality": "FAILED"}

def main():
    """Test the fixed codebase selection logic"""
    
    print("🚀 TESTING FIXED CODEBASE SELECTION")
    print("="*80)
    
    # Step 1: Unselect codebase (should now work properly)
    print("\n📋 STEP 1: Unselecting codebase")
    result1 = call_api("unselect codebase", "Unselecting codebase")
    time.sleep(3)
    
    # Step 2: Query without codebase (should fail or give generic response)
    print("\n📋 STEP 2: Query without codebase selection")
    result2 = call_api("tmwmem_alloc", "Query without codebase")
    time.sleep(3)
    
    # Step 3: Select utils codebase
    print("\n📋 STEP 3: Selecting utils codebase")
    result3 = call_api("select utils codebase", "Selecting utils codebase")
    time.sleep(3)
    
    # Step 4: Query with codebase (should give real code analysis)
    print("\n📋 STEP 4: Query with codebase selection")
    result4 = call_api("tmwmem_alloc", "Query with codebase")
    
    # Analysis
    print(f"\n{'='*80}")
    print("📊 FIXED CODEBASE SELECTION ANALYSIS")
    print(f"{'='*80}")
    
    if result2.get("success") and result4.get("success"):
        without_quality = result2.get("quality", "UNKNOWN")
        with_quality = result4.get("quality", "UNKNOWN")
        
        print(f"\n🔍 Results:")
        print(f"   WITHOUT codebase: {without_quality}")
        print(f"   WITH codebase: {with_quality}")
        
        # Determine if the fix worked
        if without_quality in ["NO_CODEBASE", "GENERIC"] and with_quality == "EXCELLENT":
            print(f"\n✅ SUCCESS: Codebase selection now works correctly!")
            print(f"   - Without selection: Proper error/generic response")
            print(f"   - With selection: Real code analysis")
        elif without_quality == with_quality:
            print(f"\n❌ STILL BROKEN: Both scenarios give same quality")
            print(f"   The sync issue may still exist")
        elif with_quality == "EXCELLENT":
            print(f"\n⚠️ PARTIAL FIX: With selection works, but without selection unclear")
        else:
            print(f"\n❓ UNCLEAR: Unexpected result pattern")
    else:
        print(f"\n❌ TEST FAILED: Could not complete both scenarios")
    
    # Specific recommendations
    print(f"\n🎯 Next Steps:")
    if result2.get("has_error_msg"):
        print(f"   ✅ Tool properly detects no codebase selection")
    elif result2.get("quality") == "GENERIC":
        print(f"   ⚠️ Tool gives generic response without selection (acceptable)")
    else:
        print(f"   ❌ Tool still auto-selects codebase (sync issue persists)")
    
    if result4.get("has_real_code"):
        print(f"   ✅ Tool works correctly with codebase selection")
    else:
        print(f"   ❌ Tool doesn't work properly even with selection")

if __name__ == "__main__":
    main()
