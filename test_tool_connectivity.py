#!/usr/bin/env python3
"""
Test what server URL the OpenWebUI tool is actually using
"""

import requests
import json

def test_openwebui_tool_debug():
    """Test the debug functionality to see what server the tool is connecting to"""
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    tool_id = "code_analyzer_tool"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    payload = {
        "model": "llama3:latest",
        "messages": [
            {
                "role": "user",
                "content": "debug tool status and show server connectivity details"
            }
        ],
        "tool_ids": [tool_id],
        "stream": False
    }
    
    print("🔧 Testing OpenWebUI tool debug to see server connectivity...")
    
    try:
        response = requests.post(
            f"{base_url}/api/chat/completions",
            headers=headers,
            json=payload,
            timeout=60
        )
        
        if response.status_code == 200:
            data = response.json()
            content = data.get('choices', [{}])[0].get('message', {}).get('content', '')
            
            print(f"✅ Debug response received ({len(content)} chars)")
            print("="*60)
            print(content)
            print("="*60)
            
            # Look for specific connectivity information
            if "code-analyzer-server:5002" in content:
                print("🔍 Found reference to code-analyzer-server:5002")
            if "405" in content:
                print("⚠️ Found 405 error in response")
            if "session" in content.lower():
                print("🔍 Found session-related information")
            if "persistence" in content.lower():
                print("🔍 Found persistence-related information")
                
        else:
            print(f"❌ Debug request failed: {response.status_code}")
            print(f"   Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Debug request error: {e}")

if __name__ == "__main__":
    test_openwebui_tool_debug()
