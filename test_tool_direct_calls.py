#!/usr/bin/env python3
"""
Test direct calls to the codebase analyzer tool methods
"""

import asyncio
import sys
import os

# Add the current directory to Python path so we can import the tool
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the tool
from open_webui_code_analyzer_tool import Tools

class MockEventEmitter:
    """Mock event emitter for testing"""
    def __init__(self, verbose=True):
        self.verbose = verbose
        self.events = []
    
    async def __call__(self, event):
        """Mock event emission"""
        if self.verbose:
            event_type = event.get("type", "unknown")
            data = event.get("data", {})
            description = data.get("description", "No description")
            done = data.get("done", False)
            status = "✅" if done else "🔄"
            print(f"   {status} Event: {description}")
        
        self.events.append(event)

class DirectToolTester:
    """Test the codebase analyzer tool methods directly"""
    
    def __init__(self):
        self.tool = Tools()
        self.emitter = MockEventEmitter(verbose=True)
        
        # Configure for our test environment
        self.tool.valves.code_analyzer_server_url = "http://home-ai-server.local:5002"
        self.tool.valves.current_codebase = "utils"
        self.tool.valves.enable_caching = False
        
        print(f"🔧 Direct Tool Tester Initialized")
        print(f"   Server: {self.tool.valves.code_analyzer_server_url}")
        print(f"   Codebase: {self.tool.valves.current_codebase}")
        print(f"   Caching: {self.tool.valves.enable_caching}")
    
    async def test_basic_methods(self):
        """Test the most basic methods first"""
        print(f"\n{'='*80}")
        print("🧪 TESTING BASIC TOOL METHODS")
        print(f"{'='*80}")
        
        tests = []
        
        # Test 1: Get server status (simplest method)
        print(f"\n[1] Testing get_server_status()...")
        try:
            result = await self.tool.get_server_status(__event_emitter__=self.emitter)
            success = "healthy" in result.lower() or "status" in result.lower()
            tests.append({"method": "get_server_status", "success": success, "result": result[:200]})
            print(f"   Result: {'✅ SUCCESS' if success else '❌ FAILED'}")
            print(f"   Preview: {result[:100]}...")
        except Exception as e:
            tests.append({"method": "get_server_status", "success": False, "error": str(e)})
            print(f"   Result: ❌ ERROR - {e}")
        
        # Test 2: List codebases
        print(f"\n[2] Testing list_codebases()...")
        try:
            result = await self.tool.list_codebases(__event_emitter__=self.emitter)
            success = "utils" in result.lower() or "codebase" in result.lower()
            tests.append({"method": "list_codebases", "success": success, "result": result[:200]})
            print(f"   Result: {'✅ SUCCESS' if success else '❌ FAILED'}")
            print(f"   Preview: {result[:100]}...")
        except Exception as e:
            tests.append({"method": "list_codebases", "success": False, "error": str(e)})
            print(f"   Result: ❌ ERROR - {e}")
        
        # Test 3: Select codebase
        print(f"\n[3] Testing select_codebase('utils')...")
        try:
            result = await self.tool.select_codebase("utils", __event_emitter__=self.emitter)
            success = "selected" in result.lower() or "utils" in result.lower()
            tests.append({"method": "select_codebase", "success": success, "result": result[:200]})
            print(f"   Result: {'✅ SUCCESS' if success else '❌ FAILED'}")
            print(f"   Preview: {result[:100]}...")
        except Exception as e:
            tests.append({"method": "select_codebase", "success": False, "error": str(e)})
            print(f"   Result: ❌ ERROR - {e}")
        
        # Test 4: Get codebase stats
        print(f"\n[4] Testing get_codebase_stats('utils')...")
        try:
            result = await self.tool.get_codebase_stats("utils", __event_emitter__=self.emitter)
            success = "chunks" in result.lower() or "files" in result.lower() or "stats" in result.lower()
            tests.append({"method": "get_codebase_stats", "success": success, "result": result[:200]})
            print(f"   Result: {'✅ SUCCESS' if success else '❌ FAILED'}")
            print(f"   Preview: {result[:100]}...")
        except Exception as e:
            tests.append({"method": "get_codebase_stats", "success": False, "error": str(e)})
            print(f"   Result: ❌ ERROR - {e}")
        
        return tests
    
    async def test_search_methods(self):
        """Test search-related methods"""
        print(f"\n{'='*80}")
        print("🔍 TESTING SEARCH METHODS")
        print(f"{'='*80}")
        
        tests = []
        
        # Test 1: Simple get_code_context
        print(f"\n[1] Testing get_code_context('tmwmem_alloc')...")
        try:
            result = await self.tool.get_code_context(
                query="tmwmem_alloc",
                codebase_name="utils",
                n_results=3,
                __event_emitter__=self.emitter
            )
            success = len(result) > 100 and ("tmwmem" in result.lower() or "function" in result.lower())
            tests.append({"method": "get_code_context", "success": success, "result": result[:200]})
            print(f"   Result: {'✅ SUCCESS' if success else '❌ FAILED'}")
            print(f"   Length: {len(result)} chars")
            print(f"   Preview: {result[:150]}...")
        except Exception as e:
            tests.append({"method": "get_code_context", "success": False, "error": str(e)})
            print(f"   Result: ❌ ERROR - {e}")
        
        # Test 2: Raw search
        print(f"\n[2] Testing raw_search('tmwmem_alloc')...")
        try:
            result = await self.tool.raw_search(
                query="tmwmem_alloc",
                codebase_name="utils",
                n_results=3,
                __event_emitter__=self.emitter
            )
            success = "chunks" in result.lower() or "results" in result.lower()
            tests.append({"method": "raw_search", "success": success, "result": result[:200]})
            print(f"   Result: {'✅ SUCCESS' if success else '❌ FAILED'}")
            print(f"   Length: {len(result)} chars")
            print(f"   Preview: {result[:150]}...")
        except Exception as e:
            tests.append({"method": "raw_search", "success": False, "error": str(e)})
            print(f"   Result: ❌ ERROR - {e}")
        
        # Test 3: Smart code context
        print(f"\n[3] Testing smart_code_context('memory allocation')...")
        try:
            result = await self.tool.smart_code_context(
                query="memory allocation",
                codebase_name="utils",
                n_results=3,
                __event_emitter__=self.emitter
            )
            success = len(result) > 100 and ("memory" in result.lower() or "alloc" in result.lower())
            tests.append({"method": "smart_code_context", "success": success, "result": result[:200]})
            print(f"   Result: {'✅ SUCCESS' if success else '❌ FAILED'}")
            print(f"   Length: {len(result)} chars")
            print(f"   Preview: {result[:150]}...")
        except Exception as e:
            tests.append({"method": "smart_code_context", "success": False, "error": str(e)})
            print(f"   Result: ❌ ERROR - {e}")
        
        return tests
    
    async def test_main_entry_point(self):
        """Test the main __call__ method that OpenWebUI would use"""
        print(f"\n{'='*80}")
        print("🎯 TESTING MAIN ENTRY POINT (__call__)")
        print(f"{'='*80}")
        
        tests = []
        
        # Test queries that should work
        test_queries = [
            "tmwmem_alloc",
            "How does memory allocation work?",
            "Show me error handling functions"
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n[{i}] Testing __call__('{query}')...")
            try:
                result = await self.tool.__call__(
                    user_query=query,
                    __event_emitter__=self.emitter
                )
                success = len(result) > 100 and (
                    "context" in result.lower() or 
                    "function" in result.lower() or
                    "code" in result.lower()
                )
                tests.append({"method": f"__call__({query})", "success": success, "result": result[:200]})
                print(f"   Result: {'✅ SUCCESS' if success else '❌ FAILED'}")
                print(f"   Length: {len(result)} chars")
                print(f"   Preview: {result[:150]}...")
            except Exception as e:
                tests.append({"method": f"__call__({query})", "success": False, "error": str(e)})
                print(f"   Result: ❌ ERROR - {e}")
        
        return tests

async def main():
    """Main test execution"""
    print("🚀 TESTING DIRECT TOOL METHOD CALLS")
    print("="*80)
    
    tester = DirectToolTester()
    
    # Test basic methods first
    basic_tests = await tester.test_basic_methods()
    
    # Test search methods
    search_tests = await tester.test_search_methods()
    
    # Test main entry point
    main_tests = await tester.test_main_entry_point()
    
    # Generate comprehensive summary
    all_tests = basic_tests + search_tests + main_tests
    print_test_summary(all_tests)

def print_test_summary(tests):
    """Print comprehensive test summary"""
    print(f"\n{'='*80}")
    print("📊 DIRECT TOOL TEST SUMMARY")
    print(f"{'='*80}")
    
    total_tests = len(tests)
    successful_tests = sum(1 for t in tests if t.get("success", False))
    
    print(f"\n🎯 OVERALL RESULTS:")
    print(f"   Total Tests: {total_tests}")
    print(f"   Successful: {successful_tests} ({successful_tests/total_tests*100:.1f}%)")
    print(f"   Failed: {total_tests - successful_tests} ({(total_tests - successful_tests)/total_tests*100:.1f}%)")
    
    # Group by category
    basic_tests = [t for t in tests if t["method"] in ["get_server_status", "list_codebases", "select_codebase", "get_codebase_stats"]]
    search_tests = [t for t in tests if t["method"] in ["get_code_context", "raw_search", "smart_code_context"]]
    main_tests = [t for t in tests if "__call__" in t["method"]]
    
    if basic_tests:
        basic_success = sum(1 for t in basic_tests if t.get("success", False))
        print(f"\n📋 BASIC METHODS: {basic_success}/{len(basic_tests)} successful")
        for test in basic_tests:
            status = "✅" if test.get("success") else "❌"
            print(f"   {status} {test['method']}")
    
    if search_tests:
        search_success = sum(1 for t in search_tests if t.get("success", False))
        print(f"\n🔍 SEARCH METHODS: {search_success}/{len(search_tests)} successful")
        for test in search_tests:
            status = "✅" if test.get("success") else "❌"
            print(f"   {status} {test['method']}")
    
    if main_tests:
        main_success = sum(1 for t in main_tests if t.get("success", False))
        print(f"\n🎯 MAIN ENTRY POINT: {main_success}/{len(main_tests)} successful")
        for test in main_tests:
            status = "✅" if test.get("success") else "❌"
            method_name = test['method'][:50] + "..." if len(test['method']) > 50 else test['method']
            print(f"   {status} {method_name}")
    
    # Show failed tests
    failed_tests = [t for t in tests if not t.get("success", False)]
    if failed_tests:
        print(f"\n❌ FAILED TESTS:")
        for test in failed_tests:
            error = test.get("error", "Unknown error")
            print(f"   ❌ {test['method']}: {error}")
    
    print(f"\n{'='*80}")

if __name__ == "__main__":
    asyncio.run(main())
