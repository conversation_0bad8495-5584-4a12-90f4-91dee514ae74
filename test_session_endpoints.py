#!/usr/bin/env python3
"""
Test the session persistence endpoints directly
"""

import requests
import json

def test_session_endpoints():
    base_url = "http://home-ai-server.local:5002"
    
    print("🔧 Testing session persistence endpoints")
    
    # Test GET endpoint
    print("\n1. Testing GET session codebase:")
    try:
        response = requests.get(f"{base_url}/tools/get_session_codebase")
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test SET endpoint
    print("\n2. Testing SET session codebase:")
    try:
        payload = {"codebase_name": "test_project"}
        response = requests.post(
            f"{base_url}/tools/set_session_codebase",
            json=payload,
            headers={"Content-Type": "application/json"}
        )
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   Error: {e}")
    
    # Test GET again to verify
    print("\n3. Testing GET session codebase after SET:")
    try:
        response = requests.get(f"{base_url}/tools/get_session_codebase")
        print(f"   Status: {response.status_code}")
        print(f"   Response: {response.json()}")
    except Exception as e:
        print(f"   Error: {e}")

if __name__ == "__main__":
    test_session_endpoints()
