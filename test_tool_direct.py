#!/usr/bin/env python3
"""
Direct test of the OpenWebUI tool functionality
Tests the tool methods directly without going through OpenWebUI API
"""

import sys
import os
import asyncio

# Add the current directory to Python path to import the tool
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Import the tool class
from open_webui_code_analyzer_tool import Tools

class MockEventEmitter:
    """Mock event emitter for testing"""
    def __init__(self):
        self.events = []
    
    async def __call__(self, event):
        self.events.append(event)
        print(f"📡 Event: {event}")

async def test_tool_directly():
    """Test the tool methods directly"""
    print("🚀 Testing OpenWebUI Code Analyzer Tool Directly")
    print("="*60)
    
    # Create tool instance
    tool = Tools()
    
    # Override the server URL to use localhost instead of Docker hostname
    tool.valves.code_analyzer_server_url = "http://localhost:5002"
    
    # Create mock event emitter
    emitter = MockEventEmitter()
    
    print(f"🔧 Server URL: {tool.valves.code_analyzer_server_url}")
    print(f"🔧 Current Codebase: {tool.valves.current_codebase}")
    
    print("\n" + "="*60)
    print("📚 STEP 1: List available codebases")
    
    try:
        result = await tool.list_codebases(__event_emitter__=emitter)
        print(f"✅ List codebases result:\n{result[:500]}...")
    except Exception as e:
        print(f"❌ List codebases failed: {e}")
    
    print("\n" + "="*60)
    print("🎯 STEP 2: Select utils codebase")
    
    try:
        result = await tool.select_codebase("utils", __event_emitter__=emitter)
        print(f"✅ Select codebase result:\n{result}")
    except Exception as e:
        print(f"❌ Select codebase failed: {e}")
    
    print("\n" + "="*60)
    print("🔍 STEP 3: Test code context retrieval")
    
    try:
        result = await tool.get_code_context(
            query="tmwmem_alloc",
            n_results=3,
            __event_emitter__=emitter
        )
        print(f"✅ Code context result:\n{result[:800]}...")
    except Exception as e:
        print(f"❌ Code context failed: {e}")
    
    print("\n" + "="*60)
    print("🔍 STEP 4: Test memory allocation query")
    
    try:
        result = await tool.get_code_context(
            query="How does memory allocation work in this codebase?",
            n_results=5,
            __event_emitter__=emitter
        )
        print(f"✅ Memory allocation query result:\n{result[:800]}...")
    except Exception as e:
        print(f"❌ Memory allocation query failed: {e}")
    
    print("\n" + "="*60)
    print("📊 STEP 5: Test statistics")
    
    try:
        result = await tool.get_codebase_statistics("utils", __event_emitter__=emitter)
        print(f"✅ Statistics result:\n{result[:500]}...")
    except Exception as e:
        print(f"❌ Statistics failed: {e}")
    
    print("\n" + "="*60)
    print("🔧 STEP 6: Test debug information")
    
    try:
        result = await tool.debug_tool_status(__event_emitter__=emitter)
        print(f"✅ Debug status result:\n{result[:800]}...")
    except Exception as e:
        print(f"❌ Debug status failed: {e}")
    
    print("\n" + "="*60)
    print("✅ Direct tool test completed!")
    
    # Print event summary
    print(f"\n📡 Total events emitted: {len(emitter.events)}")

if __name__ == "__main__":
    asyncio.run(test_tool_directly())
