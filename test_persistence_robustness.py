#!/usr/bin/env python3
"""
Comprehensive test for OpenWebUI tool persistence robustness
Tests various query types to ensure persistence works across all scenarios
"""

import requests
import json
import time

def test_openwebui_persistence_robustness():
    """Test persistence with complex and varied queries"""
    base_url = "http://home-ai-server.local:8080"
    api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
    tool_id = "code_analyzer_tool"
    
    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json"
    }
    
    # Test scenarios with different query types
    test_scenarios = [
        {
            "name": "Codebase Selection",
            "query": "select utils codebase",
            "expect_success": True,
            "expect_content": ["selected", "utils"]
        },
        {
            "name": "Function Search",
            "query": "find functions related to memory allocation",
            "expect_success": True,
            "expect_content": ["tmwmem", "alloc", "function"]
        },
        {
            "name": "Specific Symbol Search", 
            "query": "show me the tmwmem_alloc function implementation",
            "expect_success": True,
            "expect_content": ["tmwmem_alloc", "implementation", "code"]
        },
        {
            "name": "Code Analysis",
            "query": "analyze error handling patterns in this codebase",
            "expect_success": True,
            "expect_content": ["error", "handling", "pattern"]
        },
        {
            "name": "Statistics Query",
            "query": "show statistics for the current codebase",
            "expect_success": True,
            "expect_content": ["statistics", "codebase"]
        },
        {
            "name": "Complex Context Query",
            "query": "get code context for timer management and scheduling functions",
            "expect_success": True,
            "expect_content": ["timer", "context"]
        },
        {
            "name": "Data Structure Query",
            "query": "find struct definitions related to network communication",
            "expect_success": True,
            "expect_content": ["struct", "network"]
        },
        {
            "name": "Cross-Reference Query",
            "query": "show me how tmwsim functions interact with memory management",
            "expect_success": True,
            "expect_content": ["tmwsim", "memory"]
        }
    ]
    
    print("🔧 Testing OpenWebUI Tool Persistence Robustness")
    print("=" * 60)
    
    results = []
    
    for i, scenario in enumerate(test_scenarios, 1):
        print(f"\n--- Test {i}: {scenario['name']} ---")
        print(f"🔧 Query: '{scenario['query']}'")
        
        payload = {
            "model": "llama3:latest",
            "messages": [
                {
                    "role": "user", 
                    "content": scenario['query']
                }
            ],
            "tool_ids": [tool_id],
            "stream": False
        }
        
        try:
            start_time = time.time()
            response = requests.post(
                f"{base_url}/api/chat/completions",
                headers=headers,
                json=payload,
                timeout=60
            )
            end_time = time.time()
            
            if response.status_code == 200:
                data = response.json()
                content = data.get('choices', [{}])[0].get('message', {}).get('content', '')
                
                # Check if tool was used
                tool_used = len(content) > 100 and any(keyword in content.lower() for keyword in scenario['expect_content'])
                
                # Check for persistence indicators
                has_actual_code = any(indicator in content.lower() for indicator in [
                    'function', 'struct', 'implementation', 'code context', 'from the given contexts'
                ])
                
                no_selection_error = any(error in content.lower() for error in [
                    'no codebase selected', 'since there is no specific codebase', 'no specific codebase'
                ])
                
                print(f"📊 Status: {response.status_code}, Time: {end_time - start_time:.2f}s")
                print(f"💬 Length: {len(content)} chars")
                print(f"🔍 Tool used: {'✅' if tool_used else '❌'}")
                print(f"🎯 Has actual code: {'✅' if has_actual_code else '❌'}")
                print(f"⚠️ Selection error: {'❌' if no_selection_error else '✅'}")
                print(f"💬 Preview: {content[:150]}...")
                
                results.append({
                    'scenario': scenario['name'],
                    'success': response.status_code == 200,
                    'tool_used': tool_used,
                    'has_actual_code': has_actual_code,
                    'no_selection_error': not no_selection_error,
                    'response_time': end_time - start_time,
                    'content_length': len(content)
                })
                
            else:
                print(f"❌ Failed: {response.status_code}")
                print(f"   Error: {response.text}")
                results.append({
                    'scenario': scenario['name'],
                    'success': False,
                    'tool_used': False,
                    'has_actual_code': False,
                    'no_selection_error': False,
                    'response_time': end_time - start_time,
                    'content_length': 0
                })
                
        except Exception as e:
            print(f"❌ Exception: {e}")
            results.append({
                'scenario': scenario['name'],
                'success': False,
                'tool_used': False,
                'has_actual_code': False,
                'no_selection_error': False,
                'response_time': 0,
                'content_length': 0
            })
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 PERSISTENCE ROBUSTNESS SUMMARY")
    print("=" * 60)
    
    successful_tests = sum(1 for r in results if r['success'])
    tool_usage = sum(1 for r in results if r['tool_used'])
    actual_code_responses = sum(1 for r in results if r['has_actual_code'])
    no_errors = sum(1 for r in results if r['no_selection_error'])
    
    print(f"✅ Successful API calls: {successful_tests}/{len(results)}")
    print(f"🔧 Tool usage detected: {tool_usage}/{len(results)}")
    print(f"🎯 Actual code responses: {actual_code_responses}/{len(results)}")
    print(f"✅ No selection errors: {no_errors}/{len(results)}")
    
    avg_response_time = sum(r['response_time'] for r in results if r['success']) / max(successful_tests, 1)
    print(f"⏱️ Average response time: {avg_response_time:.2f}s")
    
    # Persistence assessment
    persistence_score = (actual_code_responses / len(results)) * 100
    print(f"\n🎯 PERSISTENCE SCORE: {persistence_score:.1f}%")
    
    if persistence_score >= 80:
        print("🎉 EXCELLENT: Persistence is working robustly!")
    elif persistence_score >= 60:
        print("✅ GOOD: Persistence is working well with minor issues")
    elif persistence_score >= 40:
        print("⚠️ FAIR: Persistence has some issues that need attention")
    else:
        print("❌ POOR: Persistence is not working reliably")
    
    return results

if __name__ == "__main__":
    test_openwebui_persistence_robustness()
