#!/usr/bin/env python3
"""
Test OpenWebUI API with detailed response analysis and codebase selection comparison
"""

import asyncio
import requests
import json
import time
import re

class DetailedAPITester:
    """Test OpenWebUI API with detailed response analysis"""
    
    def __init__(self):
        self.base_url = "http://home-ai-server.local:8080"
        self.api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
        self.model = "llama3:latest"
        self.tool_id = "code_analyzer_tool"  # User confirmed correct tool ID
        
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        print(f"🔧 Detailed API Tester Initialized")
        print(f"   Base URL: {self.base_url}")
        print(f"   Tool ID: {self.tool_id}")
    
    def call_api(self, query: str, timeout: int = 120) -> dict:
        """Call OpenWebUI API with detailed logging"""
        
        payload = {
            "model": self.model,
            "messages": [{"role": "user", "content": query}],
            "tool_ids": [self.tool_id],
            "stream": False
        }
        
        print(f"\n🔧 API Call: '{query}'")
        print(f"🔧 Payload: {json.dumps(payload, indent=2)}")
        
        try:
            start_time = time.time()
            response = requests.post(
                f"{self.base_url}/api/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=timeout
            )
            end_time = time.time()
            
            print(f"📊 Response: {response.status_code}, Time: {end_time - start_time:.2f}s")
            
            if response.status_code == 200:
                result = response.json()
                choices = result.get("choices", [])
                
                if choices:
                    message = choices[0].get("message", {})
                    content = message.get("content", "")
                    
                    print(f"💬 Response Content ({len(content)} chars):")
                    print("="*80)
                    print(content)
                    print("="*80)
                    
                    # Analyze for tool usage and chunks
                    self.analyze_response_content(content)
                    
                    return {
                        "success": True,
                        "content": content,
                        "response_time": end_time - start_time
                    }
                else:
                    print("❌ No choices in response")
                    return {"success": False, "error": "No choices"}
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                print(f"❌ Response: {response.text}")
                return {"success": False, "error": f"HTTP {response.status_code}"}
                
        except Exception as e:
            print(f"❌ Exception: {e}")
            return {"success": False, "error": str(e)}
    
    def analyze_response_content(self, content: str):
        """Analyze response content for tool usage and chunk information"""
        
        # Look for tool usage indicators
        tool_indicators = [
            "code context retrieved", "chunks found", "codebase analyzer",
            "=== relevant code context ===", "=== code metadata ===",
            "context 1:", "context 2:", "file:", "language:", "type:",
            "domains:", "quality:", "complexity:"
        ]
        
        found_indicators = [ind for ind in tool_indicators if ind in content.lower()]
        
        # Look for chunk counts
        chunk_patterns = [
            r'(\d+)\s+chunks?\s+found',
            r'code context retrieved successfully \((\d+) chunks?\s+found\)',
            r'retrieved successfully \((\d+) chunks?\)',
            r'found (\d+) relevant',
            r'(\d+) relevant code sections'
        ]
        
        chunk_matches = []
        for pattern in chunk_patterns:
            matches = re.findall(pattern, content.lower())
            if matches:
                chunk_matches.extend(matches)
        
        # Look for actual code content
        code_indicators = [
            "tmwmem", "tmwdiag", "void *", "malloc", "alloc", "struct",
            "#include", "typedef", "static", "return"
        ]
        
        found_code = [ind for ind in code_indicators if ind in content.lower()]
        
        # Check for generic responses (not our codebase)
        generic_indicators = [
            "tensorflow", "tmw.js", "javascript", "node.js", "python",
            "general programming", "typical implementation"
        ]
        
        found_generic = [ind for ind in generic_indicators if ind in content.lower()]
        
        print(f"\n🔍 Content Analysis:")
        print(f"   Tool Indicators: {found_indicators}")
        print(f"   Chunk Numbers: {chunk_matches}")
        print(f"   Code Content: {found_code[:5]}...")  # Show first 5
        print(f"   Generic Response: {found_generic}")
        
        # Overall assessment
        tool_used = len(found_indicators) > 0
        has_chunks = len(chunk_matches) > 0
        has_real_code = len(found_code) > 2
        is_generic = len(found_generic) > 0
        
        print(f"   Assessment:")
        print(f"     Tool Used: {tool_used}")
        print(f"     Has Chunks: {has_chunks} ({chunk_matches[0] if chunk_matches else 'N/A'})")
        print(f"     Real Code: {has_real_code}")
        print(f"     Generic: {is_generic}")

async def test_with_codebase_selection():
    """Test with explicit codebase selection"""
    print("🚀 TESTING WITH CODEBASE SELECTION")
    print("="*80)
    
    tester = DetailedAPITester()
    
    # Step 1: Select codebase
    print("\n📋 STEP 1: Selecting utils codebase")
    select_result = tester.call_api("select utils codebase")
    
    if not select_result.get("success"):
        print("❌ Failed to select codebase")
        return []
    
    # Step 2: Test queries
    test_queries = [
        "tmwmem_alloc",
        "How does memory allocation work in this codebase?"
    ]
    
    results = []
    for query in test_queries:
        print(f"\n📋 STEP 2: Testing query after codebase selection")
        result = tester.call_api(query)
        results.append({"query": query, "result": result})
        await asyncio.sleep(2)
    
    return results

async def test_without_codebase_selection():
    """Test without explicit codebase selection"""
    print("\n\n🚀 TESTING WITHOUT CODEBASE SELECTION")
    print("="*80)
    
    tester = DetailedAPITester()
    
    # Test queries directly (no codebase selection)
    test_queries = [
        "tmwmem_alloc",
        "How does memory allocation work in this codebase?"
    ]
    
    results = []
    for query in test_queries:
        print(f"\n📋 Testing query without codebase selection")
        result = tester.call_api(query)
        results.append({"query": query, "result": result})
        await asyncio.sleep(2)
    
    return results

async def main():
    """Main test execution"""
    
    # Test with codebase selection
    results_with = await test_with_codebase_selection()
    
    # Test without codebase selection
    results_without = await test_without_codebase_selection()
    
    # Compare results
    print(f"\n{'='*80}")
    print("📊 COMPARISON SUMMARY")
    print(f"{'='*80}")
    
    print(f"\n🎯 WITH codebase selection:")
    for i, item in enumerate(results_with, 1):
        query = item["query"]
        success = item["result"].get("success", False)
        print(f"   {i}. '{query}': {'✅' if success else '❌'}")
    
    print(f"\n🎯 WITHOUT codebase selection:")
    for i, item in enumerate(results_without, 1):
        query = item["query"]
        success = item["result"].get("success", False)
        print(f"   {i}. '{query}': {'✅' if success else '❌'}")
    
    # Answer the key questions
    print(f"\n🔍 KEY FINDINGS:")
    
    with_success = sum(1 for item in results_with if item["result"].get("success"))
    without_success = sum(1 for item in results_without if item["result"].get("success"))
    
    if with_success > without_success:
        print("✅ Codebase selection IMPROVES results!")
    elif with_success == without_success:
        print("⚠️ Codebase selection makes NO DIFFERENCE")
    else:
        print("❌ Codebase selection WORSENS results")
    
    print(f"📊 Success rates: WITH={with_success}/{len(results_with)}, WITHOUT={without_success}/{len(results_without)}")

if __name__ == "__main__":
    asyncio.run(main())
