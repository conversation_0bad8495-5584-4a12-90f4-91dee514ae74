#!/usr/bin/env python3
"""
Test script for current codebase selection and query functionality
Tests both the new stateless endpoints and legacy endpoints with current_codebase
"""

import requests
import json
import time
from typing import Dict, Any, Optional

class CodeAnalyzerTester:
    def __init__(self, base_url: str = "http://localhost:5002"):
        self.base_url = base_url
        self.session = requests.Session()
        
    def test_server_health(self) -> bool:
        """Test if the server is running and healthy"""
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                print("✅ Server is healthy")
                return True
            else:
                print(f"❌ Server health check failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Cannot connect to server: {e}")
            return False
    
    def list_available_codebases(self) -> list:
        """List all available codebases"""
        try:
            response = self.session.post(f"{self.base_url}/tools/list_codebases")
            if response.status_code == 200:
                data = response.json()
                result_text = data.get('result', '')
                print(f"📚 Available codebases:\n{result_text}")
                
                # Extract codebase names from the result
                lines = result_text.split('\n')
                codebases = []
                for line in lines:
                    if '**⚠️' in line or '**✅' in line:
                        # Extract codebase name from format like "**⚠️ utils**" or "**✅ utils**"
                        parts = line.split('**')
                        if len(parts) >= 2:
                            name_part = parts[1].strip()
                            if name_part.startswith('⚠️ ') or name_part.startswith('✅ '):
                                codebase_name = name_part[2:].strip()  # Remove emoji and space
                                codebases.append(codebase_name)

                return codebases
            else:
                print(f"❌ Failed to list codebases: {response.status_code}")
                return []
        except Exception as e:
            print(f"❌ Error listing codebases: {e}")
            return []
    
    def select_codebase(self, codebase_name: str) -> bool:
        """Select a codebase for legacy endpoints"""
        try:
            payload = {"codebase_name": codebase_name}
            response = self.session.post(f"{self.base_url}/tools/select_codebase", json=payload)
            
            if response.status_code == 200:
                data = response.json()
                result = data.get('result', '')
                print(f"🎯 Codebase selection result:\n{result}")
                return "✅" in result
            else:
                print(f"❌ Failed to select codebase: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Error selecting codebase: {e}")
            return False
    
    def test_stateless_search(self, codebase_name: str, query: str) -> Dict[str, Any]:
        """Test the stateless enhanced search endpoint"""
        try:
            payload = {
                "codebase_name": codebase_name,
                "query": query,
                "n_results": 5
            }
            response = self.session.post(f"{self.base_url}/tools/enhanced_search", json=payload)
            
            if response.status_code == 200:
                data = response.json()
                result = data.get('result', '')
                print(f"🔍 Stateless search results for '{query}' in {codebase_name}:")
                print(f"{result[:500]}..." if len(result) > 500 else result)
                return {"success": True, "result": result}
            else:
                print(f"❌ Stateless search failed: {response.status_code}")
                return {"success": False, "error": f"HTTP {response.status_code}"}
        except Exception as e:
            print(f"❌ Error in stateless search: {e}")
            return {"success": False, "error": str(e)}
    
    def test_legacy_stats(self) -> Dict[str, Any]:
        """Test the legacy /stats endpoint that requires current_codebase"""
        try:
            response = self.session.get(f"{self.base_url}/stats")
            
            if response.status_code == 200:
                data = response.json()
                print(f"📊 Legacy stats: {json.dumps(data, indent=2)}")
                return {"success": True, "data": data}
            else:
                print(f"❌ Legacy stats failed: {response.status_code}")
                if response.status_code == 400:
                    print("   This might be expected if no codebase is selected")
                return {"success": False, "status_code": response.status_code}
        except Exception as e:
            print(f"❌ Error in legacy stats: {e}")
            return {"success": False, "error": str(e)}
    
    def test_legacy_context(self, query: str) -> Dict[str, Any]:
        """Test the legacy /context endpoint that requires current_codebase"""
        try:
            payload = {"query": query, "n_results": 3}
            response = self.session.post(f"{self.base_url}/context", json=payload)

            if response.status_code == 200:
                data = response.json()
                context = data.get('context', '')
                print(f"🎯 Legacy context for '{query}':")
                print(f"{context[:300]}..." if len(context) > 300 else context)
                return {"success": True, "data": data}
            else:
                print(f"❌ Legacy context failed: {response.status_code}")
                return {"success": False, "status_code": response.status_code}
        except Exception as e:
            print(f"❌ Error in legacy context: {e}")
            return {"success": False, "error": str(e)}

    def process_codebase(self, codebase_name: str) -> bool:
        """Process a codebase to make it ready for testing"""
        try:
            payload = {"codebase_name": codebase_name}
            print(f"⚙️ Processing codebase '{codebase_name}' (this may take a while)...")
            response = self.session.post(f"{self.base_url}/tools/process_codebase", json=payload)

            if response.status_code == 200:
                data = response.json()
                result = data.get('result', '')
                print(f"✅ Processing completed:\n{result[:500]}..." if len(result) > 500 else result)
                return True
            else:
                print(f"❌ Failed to process codebase: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ Error processing codebase: {e}")
            return False
    
    def run_comprehensive_test(self):
        """Run a comprehensive test of all functionality"""
        print("🚀 Starting comprehensive test of code analyzer functionality\n")
        
        # 1. Test server health
        if not self.test_server_health():
            print("❌ Server is not healthy, aborting tests")
            return
        
        print("\n" + "="*60)
        
        # 2. List available codebases
        print("\n📚 STEP 1: Listing available codebases")
        codebases = self.list_available_codebases()

        if not codebases:
            print("❌ No codebases found, cannot continue tests")
            return

        # Use a small codebase for testing (prefer utils or test_project)
        test_codebase = None
        preferred_codebases = ['utils', 'test_project', 'modbus']
        for preferred in preferred_codebases:
            if preferred in codebases:
                test_codebase = preferred
                break

        if not test_codebase:
            test_codebase = codebases[0]  # Fallback to first available

        print(f"\n🎯 Using '{test_codebase}' for testing")

        # 3. Process the codebase to make it ready for testing
        print(f"\n⚙️ STEP 2: Processing codebase '{test_codebase}'")
        if not self.process_codebase(test_codebase):
            print("❌ Failed to process codebase, cannot continue tests")
            return
        
        print("\n" + "="*60)

        # 4. Test stateless functionality (should work without selection)
        print(f"\n🔍 STEP 3: Testing stateless search (no selection needed)")
        test_queries = ["memory", "function", "struct", "malloc"]

        for query in test_queries:
            print(f"\n--- Testing query: '{query}' ---")
            result = self.test_stateless_search(test_codebase, query)
            time.sleep(1)  # Brief pause between requests

        print("\n" + "="*60)

        # 5. Test legacy endpoints without selection (should fail)
        print(f"\n📊 STEP 4: Testing legacy endpoints WITHOUT codebase selection")
        print("(These should fail with 'No codebase selected' error)")

        print("\n--- Testing legacy stats ---")
        self.test_legacy_stats()

        print("\n--- Testing legacy context ---")
        self.test_legacy_context("memory management")

        print("\n" + "="*60)

        # 6. Select a codebase
        print(f"\n🎯 STEP 5: Selecting codebase '{test_codebase}'")
        if not self.select_codebase(test_codebase):
            print("❌ Failed to select codebase, cannot test legacy endpoints")
            return

        print("\n" + "="*60)

        # 7. Test legacy endpoints with selection (should work)
        print(f"\n📊 STEP 6: Testing legacy endpoints WITH codebase selection")
        
        print("\n--- Testing legacy stats ---")
        self.test_legacy_stats()
        
        print("\n--- Testing legacy context ---")
        self.test_legacy_context("memory management")
        
        print("\n" + "="*60)
        print("\n✅ Comprehensive test completed!")

if __name__ == "__main__":
    tester = CodeAnalyzerTester()
    tester.run_comprehensive_test()
