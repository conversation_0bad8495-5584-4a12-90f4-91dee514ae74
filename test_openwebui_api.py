#!/usr/bin/env python3
"""
Test OpenWebUI API with codebase analyzer tool
"""

import asyncio
import json
import requests
import time
from typing import List, Dict, Any

class OpenWebUIAPITester:
    """Test the codebase analyzer tool via OpenWebUI API"""
    
    def __init__(self):
        # OpenWebUI configuration from your setup
        self.base_url = "http://home-ai-server.local:8080"
        self.api_key = "sk-320242e0335e45a4b1fa4752f758f9ab"
        # Try different models that might support tools
        self.models_to_try = [
            "llama3.1:latest",
            "llama3.2:latest",
            "llama3:latest",
            "qwen2.5:latest",
            "mistral:latest"
        ]
        self.model = None  # Will be set after finding a compatible model
        
        self.headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        print(f"🔧 OpenWebUI API Tester Initialized")
        print(f"   Base URL: {self.base_url}")
        print(f"   Models to try: {self.models_to_try}")

        # Find a compatible model
        self.model = self.find_compatible_model()

    def find_compatible_model(self) -> str:
        """Find a model that supports function calling"""
        try:
            # Get available models
            response = requests.get(f"{self.base_url}/api/models", headers=self.headers, timeout=10)
            if response.status_code == 200:
                available_models = [model.get("id", "") for model in response.json()]
                print(f"📋 Available models: {available_models}")

                # Try to find a compatible model from our list
                for model in self.models_to_try:
                    if model in available_models:
                        print(f"✅ Selected model: {model}")
                        return model

                # If none of our preferred models are available, try the first available
                if available_models:
                    fallback_model = available_models[0]
                    print(f"⚠️ Using fallback model: {fallback_model}")
                    return fallback_model
                else:
                    print(f"❌ No models available")
                    return "llama3:latest"  # Default fallback
            else:
                print(f"❌ Could not get models list: {response.status_code}")
                return "llama3:latest"  # Default fallback
        except Exception as e:
            print(f"❌ Error finding compatible model: {e}")
            return "llama3:latest"  # Default fallback

    def test_api_connection(self) -> bool:
        """Test basic API connectivity"""
        try:
            response = requests.get(f"{self.base_url}/api/models", headers=self.headers, timeout=10)
            if response.status_code == 200:
                models = response.json()
                print(f"✅ API Connection successful. Found {len(models)} models.")
                return True
            else:
                print(f"❌ API Connection failed: {response.status_code}")
                return False
        except Exception as e:
            print(f"❌ API Connection error: {e}")
            return False

    def find_correct_tool_id(self) -> str:
        """Find the correct tool ID by testing different possibilities"""
        print("🔍 Finding correct tool ID...")

        # Based on user confirmation and previous investigations
        tool_ids_to_test = [
            "code_analyzer_tool",       # CORRECT: User confirmed this is the right one
            "code_analyzer_tools",      # Backup that worked faster
            "codebase_analyzer",        # Previous attempt
            "code_analysis_tool",       # Variation
            "code_rag_tools",          # Previous name from memories
        ]

        test_query = "tmwmem_alloc"

        for tool_id in tool_ids_to_test:
            print(f"   Testing tool_id: '{tool_id}'...")
            try:
                result = self.call_codebase_analyzer(test_query, tool_id, timeout=120)  # Increased timeout
                if result.get("success"):
                    content = result.get("content", "")
                    # Check if it looks like our tool was actually called
                    if any(indicator in content.lower() for indicator in [
                        "code context retrieved", "chunks found", "tmwmem", "utils codebase"
                    ]):
                        print(f"   ✅ Found working tool_id: '{tool_id}'")
                        return tool_id
                    else:
                        print(f"   ⚠️ Tool responded but may not be our codebase analyzer")
                else:
                    print(f"   ❌ Failed: {result.get('error', 'Unknown error')}")
            except Exception as e:
                print(f"   ❌ Error testing '{tool_id}': {e}")

        print("   ⚠️ No working tool_id found, using default 'codebase_analyzer'")
        return "codebase_analyzer"

    def select_utils_codebase(self, tool_id: str) -> bool:
        """Select the utils codebase before running tests"""
        print("🔧 Selecting utils codebase...")

        try:
            result = self.call_codebase_analyzer("select utils codebase", tool_id, timeout=120)
            if result.get("success"):
                content = result.get("content", "")
                success = any(indicator in content.lower() for indicator in [
                    "utils", "selected", "codebase", "ready"
                ])
                if success:
                    print("   ✅ Utils codebase selected successfully")
                    return True
                else:
                    print("   ⚠️ Codebase selection response unclear")
                    return False
            else:
                print(f"   ❌ Failed to select codebase: {result.get('error', 'Unknown error')}")
                return False
        except Exception as e:
            print(f"   ❌ Error selecting codebase: {e}")
            return False

    def call_codebase_analyzer(self, query: str, tool_id: str = "codebase_analyzer", timeout: int = 60) -> Dict[str, Any]:
        """Call OpenWebUI with explicit tool_ids to trigger the codebase analyzer tool"""

        # Use the query directly - the tool_ids parameter will ensure our tool is used
        payload = {
            "model": self.model,
            "messages": [
                {
                    "role": "user",
                    "content": query
                }
            ],
            "tool_ids": [tool_id],  # This is the key! Explicitly specify our tool
            "stream": False
        }
        
        print(f"\n🔧 Calling OpenWebUI API with query: '{query}'")
        print(f"🔧 Tool ID: '{tool_id}', Timeout: {timeout}s")

        try:
            start_time = time.time()
            response = requests.post(
                f"{self.base_url}/api/chat/completions",
                headers=self.headers,
                json=payload,
                timeout=timeout  # Use the timeout parameter
            )
            end_time = time.time()

            print(f"📊 Response Status: {response.status_code}, Time: {end_time - start_time:.2f}s")

            if response.status_code == 200:
                result = response.json()

                # Extract information from the response
                choices = result.get("choices", [])
                if choices:
                    message = choices[0].get("message", {})
                    content = message.get("content", "")
                    tool_calls = message.get("tool_calls", [])

                    # Show detailed response content
                    print(f"💬 Response Content (first 300 chars):")
                    print(f"   {content[:300]}...")

                    # Look for chunk indicators
                    chunk_indicators = [
                        "chunks found", "chunk found", "code context retrieved",
                        "context retrieved", "relevant code", "results found"
                    ]
                    found_indicators = [ind for ind in chunk_indicators if ind in content.lower()]
                    if found_indicators:
                        print(f"🔍 Found chunk indicators: {found_indicators}")

                    # Look for chunk numbers
                    import re
                    chunk_matches = re.findall(r'(\d+)\s+chunks?\s+found', content.lower())
                    if chunk_matches:
                        print(f"🔢 Chunk numbers found: {chunk_matches}")

                    return {
                        "success": True,
                        "content": content,
                        "tool_calls": tool_calls,
                        "response_time": end_time - start_time,
                        "raw_response": result
                    }
                else:
                    print("❌ No choices in response")
                    return {
                        "success": False,
                        "error": "No choices in response",
                        "raw_response": result
                    }
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                return {
                    "success": False,
                    "error": f"HTTP {response.status_code}: {response.text}",
                    "response_time": end_time - start_time
                }

        except Exception as e:
            print(f"❌ Exception: {e}")
            return {
                "success": False,
                "error": f"Request failed: {str(e)}"
            }
    
    def analyze_tool_response(self, result: Dict[str, Any], query: str) -> Dict[str, Any]:
        """Analyze the tool response for success metrics"""
        analysis = {
            "query": query,
            "success": result.get("success", False),
            "response_time": result.get("response_time", 0),
            "tool_called": False,
            "chunks_found": 0,
            "has_code_context": False,
            "error": result.get("error")
        }
        
        if result.get("success"):
            content = result.get("content", "")
            tool_calls = result.get("tool_calls", [])
            
            # Check if tool was called
            analysis["tool_called"] = len(tool_calls) > 0
            
            # Look for chunk count in content
            import re
            chunk_matches = re.findall(r'(\d+)\s+chunks?\s+found', content.lower())
            if chunk_matches:
                analysis["chunks_found"] = int(chunk_matches[-1])  # Take the last match
            
            # Check for code context and tool usage indicators
            analysis["has_code_context"] = any(keyword in content.lower() for keyword in [
                "code context", "function", "struct", "class", "method", "variable",
                "tmwmem", "tmwdiag", "utils codebase", "memory allocation", "error handling"
            ])

            # Check for specific indicators that the codebase analyzer was used
            tool_indicators = [
                "code context retrieved",
                "chunks found",
                "codebase analyzer",
                "=== code metadata ===",
                "=== relevant code context ===",
                "file:", "language:", "type:",
                "tmwmem_alloc", "tmwdiag_error", "tmwmem", "tmwdiag",
                "context 1:", "context 2:",
                "domains:", "quality:", "complexity:"
            ]
            analysis["tool_called"] = any(indicator in content.lower() for indicator in tool_indicators)

            # Also check for the specific success pattern we saw in GUI
            if "code context retrieved successfully" in content.lower():
                analysis["tool_called"] = True
            
            # Overall success criteria
            analysis["overall_success"] = (
                analysis["tool_called"] and 
                (analysis["chunks_found"] > 0 or analysis["has_code_context"])
            )
        
        return analysis

async def test_with_codebase_selection():
    """Test with codebase selection"""
    print("🚀 TESTING WITH CODEBASE SELECTION")
    print("="*80)

    tester = OpenWebUIAPITester()

    # Test API connection first
    if not tester.test_api_connection():
        print("❌ Cannot proceed without API connection")
        return []

    # Find the correct tool ID
    correct_tool_id = tester.find_correct_tool_id()
    print(f"✅ Using tool_id: '{correct_tool_id}'")

    # Select utils codebase before running tests
    if not tester.select_utils_codebase(correct_tool_id):
        print("⚠️ Warning: Could not confirm codebase selection, proceeding anyway...")
    else:
        print("✅ Utils codebase is ready for testing")

    return await run_test_queries(tester, correct_tool_id, "WITH codebase selection")

async def test_without_codebase_selection():
    """Test without codebase selection"""
    print("\n\n🚀 TESTING WITHOUT CODEBASE SELECTION")
    print("="*80)

    tester = OpenWebUIAPITester()

    # Test API connection first
    if not tester.test_api_connection():
        print("❌ Cannot proceed without API connection")
        return []

    # Find the correct tool ID (but don't select codebase)
    correct_tool_id = tester.find_correct_tool_id()
    print(f"✅ Using tool_id: '{correct_tool_id}'")
    print("⚠️ Skipping codebase selection to test difference")

    return await run_test_queries(tester, correct_tool_id, "WITHOUT codebase selection")

async def run_test_queries(tester, correct_tool_id, test_name):
    """Run the actual test queries"""
    
    # Test queries that should work with our improvements
    test_queries = [
        # Exact function names
        "tmwmem_alloc",
        "tmwdiag_error",
        
        # Natural language queries
        "How does memory allocation work in this codebase?",
        "Show me error handling functions",
        "Find memory allocation functions",
        "Tell me about buffer management in the utils codebase",
        
        # Constants/macros
        "TMWMEM_HEADER",
        
        # Architecture queries
        "What are the main modules in this codebase?",
        "How is the TMW library structured?"
    ]
    
    results = []
    
    print(f"\n🧪 Testing {len(test_queries)} queries...")
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n[{i}/{len(test_queries)}] Testing: '{query}'")
        print("-" * 60)
        
        # Call OpenWebUI API with explicit tool_ids and increased timeout
        result = tester.call_codebase_analyzer(query, correct_tool_id, timeout=120)

        # Analyze the response
        analysis = tester.analyze_tool_response(result, query)
        results.append(analysis)
        
        # Print immediate results
        if analysis["success"]:
            status = "✅ SUCCESS" if analysis["overall_success"] else "⚠️ PARTIAL"
            print(f"   Result: {status}")
            print(f"   Tool Called: {analysis['tool_called']}")
            print(f"   Chunks Found: {analysis['chunks_found']}")
            print(f"   Has Code Context: {analysis['has_code_context']}")
            print(f"   Response Time: {analysis['response_time']:.2f}s")
        else:
            print(f"   Result: ❌ FAILED")
            print(f"   Error: {analysis['error']}")
        
        # Small delay between requests
        await asyncio.sleep(2)
    
    # Generate comprehensive summary
    print_api_test_summary(results)

def print_api_test_summary(results: List[Dict[str, Any]]):
    """Print comprehensive test summary"""
    print(f"\n{'='*80}")
    print("📊 OPENWEBUI API TEST SUMMARY")
    print(f"{'='*80}")
    
    total_tests = len(results)
    successful_tests = sum(1 for r in results if r.get("overall_success", False))
    tool_called_tests = sum(1 for r in results if r.get("tool_called", False))
    chunks_found_tests = sum(1 for r in results if r.get("chunks_found", 0) > 0)
    
    print(f"\n🎯 OVERALL RESULTS:")
    print(f"   Total Tests: {total_tests}")
    print(f"   Overall Success: {successful_tests} ({successful_tests/total_tests*100:.1f}%)")
    print(f"   Tool Called: {tool_called_tests} ({tool_called_tests/total_tests*100:.1f}%)")
    print(f"   Chunks Found: {chunks_found_tests} ({chunks_found_tests/total_tests*100:.1f}%)")
    
    # Average response time
    response_times = [r.get("response_time", 0) for r in results if r.get("response_time")]
    if response_times:
        avg_time = sum(response_times) / len(response_times)
        print(f"   Average Response Time: {avg_time:.2f}s")
    
    # Detailed results by query type
    print(f"\n📋 DETAILED RESULTS:")
    
    exact_queries = [r for r in results if len(r["query"].split()) == 1]
    natural_queries = [r for r in results if len(r["query"].split()) > 3]
    
    if exact_queries:
        exact_success = sum(1 for r in exact_queries if r.get("overall_success", False))
        print(f"   Exact Function Names: {exact_success}/{len(exact_queries)} successful")
    
    if natural_queries:
        natural_success = sum(1 for r in natural_queries if r.get("overall_success", False))
        print(f"   Natural Language: {natural_success}/{len(natural_queries)} successful")
    
    # Show individual results
    print(f"\n🔍 INDIVIDUAL RESULTS:")
    for i, result in enumerate(results, 1):
        status = "✅" if result.get("overall_success") else "❌"
        chunks = result.get("chunks_found", 0)
        tool_called = "🔧" if result.get("tool_called") else "❌"
        query = result["query"][:50] + "..." if len(result["query"]) > 50 else result["query"]
        
        print(f"   {i:2d}. {status} {tool_called} '{query}' - {chunks} chunks")
    
    # Success analysis
    print(f"\n🎯 SUCCESS ANALYSIS:")
    if successful_tests == total_tests:
        print("   🎉 PERFECT! All queries succeeded via OpenWebUI API!")
    elif successful_tests > total_tests * 0.8:
        print("   ✅ EXCELLENT! Most queries succeeded via OpenWebUI API!")
    elif successful_tests > total_tests * 0.5:
        print("   ⚠️ MIXED: Some queries succeeded - may need investigation")
    else:
        print("   ❌ POOR: Most queries failed - significant issues present")
    
    print(f"\n{'='*80}")

if __name__ == "__main__":
    asyncio.run(main())
