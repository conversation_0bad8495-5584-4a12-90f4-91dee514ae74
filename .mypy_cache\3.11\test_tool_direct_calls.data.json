{".class": "MypyFile", "_fullname": "test_tool_direct_calls", "future_import_flags": [], "is_partial_stub_package": false, "is_stub": false, "names": {".class": "SymbolTable", "DirectToolTester": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "test_tool_direct_calls.DirectToolTester", "name": "DirectToolTester", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "test_tool_direct_calls.DirectToolTester", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "test_tool_direct_calls", "mro": ["test_tool_direct_calls.DirectToolTester", "builtins.object"], "names": {".class": "SymbolTable", "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_tool_direct_calls.DirectToolTester.__init__", "name": "__init__", "type": null}}, "emitter": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "test_tool_direct_calls.DirectToolTester.emitter", "name": "emitter", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "test_basic_methods": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "test_tool_direct_calls.DirectToolTester.test_basic_methods", "name": "test_basic_methods", "type": null}}, "test_main_entry_point": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "test_tool_direct_calls.DirectToolTester.test_main_entry_point", "name": "test_main_entry_point", "type": null}}, "test_search_methods": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["self"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "test_tool_direct_calls.DirectToolTester.test_search_methods", "name": "test_search_methods", "type": null}}, "tool": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "test_tool_direct_calls.DirectToolTester.tool", "name": "tool", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "test_tool_direct_calls.DirectToolTester.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "test_tool_direct_calls.DirectToolTester", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "MockEventEmitter": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "TypeInfo", "_promote": [], "abstract_attributes": [], "alt_promote": null, "bases": ["builtins.object"], "dataclass_transform_spec": null, "declared_metaclass": null, "defn": {".class": "ClassDef", "fullname": "test_tool_direct_calls.MockEventEmitter", "name": "MockEventEmitter", "type_vars": []}, "deletable_attributes": [], "deprecated": null, "flags": [], "fullname": "test_tool_direct_calls.MockEventEmitter", "has_param_spec_type": false, "metaclass_type": null, "metadata": {}, "module_name": "test_tool_direct_calls", "mro": ["test_tool_direct_calls.MockEventEmitter", "builtins.object"], "names": {".class": "SymbolTable", "__call__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 0], "arg_names": ["self", "event"], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "test_tool_direct_calls.MockEventEmitter.__call__", "name": "__call__", "type": null}}, "__init__": {".class": "SymbolTableNode", "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0, 1], "arg_names": ["self", "verbose"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_tool_direct_calls.MockEventEmitter.__init__", "name": "__init__", "type": null}}, "events": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "test_tool_direct_calls.MockEventEmitter.events", "name": "events", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}, "verbose": {".class": "SymbolTableNode", "implicit": true, "kind": "<PERSON><PERSON><PERSON>", "node": {".class": "Var", "flags": ["is_inferred"], "fullname": "test_tool_direct_calls.MockEventEmitter.verbose", "name": "verbose", "type": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 1}}}}, "self_type": {".class": "TypeVarType", "default": {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 4}, "fullname": "test_tool_direct_calls.MockEventEmitter.Self", "id": 0, "name": "Self", "namespace": "", "upper_bound": "test_tool_direct_calls.MockEventEmitter", "values": [], "variance": 0}, "slots": null, "tuple_type": null, "type_vars": [], "typeddict_type": null}}, "Tools": {".class": "SymbolTableNode", "cross_ref": "open_webui_code_analyzer_tool.Tools", "kind": "Gdef"}, "__annotations__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_tool_direct_calls.__annotations__", "name": "__annotations__", "type": {".class": "Instance", "args": ["builtins.str", {".class": "AnyType", "missing_import_name": null, "source_any": null, "type_of_any": 6}], "extra_attrs": null, "type_ref": "builtins.dict"}}}, "__doc__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_tool_direct_calls.__doc__", "name": "__doc__", "type": "builtins.str"}}, "__file__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_tool_direct_calls.__file__", "name": "__file__", "type": "builtins.str"}}, "__name__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_tool_direct_calls.__name__", "name": "__name__", "type": "builtins.str"}}, "__package__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_tool_direct_calls.__package__", "name": "__package__", "type": "builtins.str"}}, "__spec__": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "Var", "flags": ["is_ready"], "fullname": "test_tool_direct_calls.__spec__", "name": "__spec__", "type": "_frozen_importlib.ModuleSpec"}}, "asyncio": {".class": "SymbolTableNode", "cross_ref": "asyncio", "kind": "Gdef"}, "main": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [], "arg_names": [], "dataclass_transform_spec": null, "deprecated": null, "flags": ["is_coroutine"], "fullname": "test_tool_direct_calls.main", "name": "main", "type": null}}, "os": {".class": "SymbolTableNode", "cross_ref": "os", "kind": "Gdef"}, "print_test_summary": {".class": "SymbolTableNode", "kind": "Gdef", "node": {".class": "FuncDef", "abstract_status": 0, "arg_kinds": [0], "arg_names": ["tests"], "dataclass_transform_spec": null, "deprecated": null, "flags": [], "fullname": "test_tool_direct_calls.print_test_summary", "name": "print_test_summary", "type": null}}, "sys": {".class": "SymbolTableNode", "cross_ref": "sys", "kind": "Gdef"}}, "path": "C:\\home-repos\\openwebui_rag_code_server\\test_tool_direct_calls.py"}